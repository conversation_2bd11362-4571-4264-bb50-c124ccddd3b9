<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\MobSkillTemplate;

class UpdateMobSkillTemplate extends Command
{
    protected $signature = 'mob:update-skill-template {template_id} {--trigger-chance=} {--effect-duration=} {--effect-value=}';
    protected $description = 'Обновить шаблон скилла моба';

    public function handle()
    {
        $templateId = $this->argument('template_id');

        // Находим шаблон
        $template = MobSkillTemplate::find($templateId);
        if (!$template) {
            $this->error("Шаблон скилла с ID '{$templateId}' не найден");
            return 1;
        }

        $this->info("Обновление шаблона скилла: {$template->name} (ID: {$template->id})");

        $updated = false;

        if ($this->option('trigger-chance') !== null) {
            $triggerChance = (int) $this->option('trigger-chance');
            $template->trigger_chance = $triggerChance;
            $this->info("Установлен шанс срабатывания: {$triggerChance}%");
            $updated = true;
        }

        if ($this->option('effect-duration') !== null) {
            $effectDuration = (int) $this->option('effect-duration');
            $template->effect_duration = $effectDuration;
            $this->info("Установлена длительность эффекта: {$effectDuration} сек");
            $updated = true;
        }

        if ($this->option('effect-value') !== null) {
            $effectValue = (int) $this->option('effect-value');
            $template->effect_value = $effectValue;
            $this->info("Установлено значение эффекта: {$effectValue}");
            $updated = true;
        }

        if ($updated) {
            $template->save();
            $this->info("✅ Шаблон скилла успешно обновлен!");
        } else {
            $this->info("Нет изменений для применения");
        }

        return 0;
    }
}
