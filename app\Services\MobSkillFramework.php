<?php

namespace App\Services;

use App\Models\Mob;
use App\Models\MobSkill;
use App\Models\MobSkillTemplate;
use App\Models\User;
use App\Models\ActiveEffect;
use App\Services\PlayerHealthService;
use App\Services\BattleLogService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

/**
 * Расширяемый фреймворк для системы скиллов мобов
 * 
 * Этот фреймворк предоставляет основу для создания сложных систем скиллов мобов:
 * - Кулдауны и шансы активации
 * - Различные типы эффектов (урон, дебафы, бафы)
 * - Условная логика использования скиллов
 * - Интеграция с системой обнаружения
 * - Легко расширяемая архитектура
 */
class MobSkillFramework
{
    /**
     * Типы скиллов мобов
     */
    const SKILL_TYPE_DAMAGE = 'damage';           // Урон по цели
    const SKILL_TYPE_DEBUFF = 'debuff';           // Дебаф на цель
    const SKILL_TYPE_STUN = 'stun';               // Оглушение
    const SKILL_TYPE_AOE = 'aoe';                 // Область поражения
    const SKILL_TYPE_SELF_BUFF = 'self_buff';     // Баф на себя
    const SKILL_TYPE_DETECTION = 'detection';     // Обнаружение игроков
    const SKILL_TYPE_SUMMON = 'summon';           // Призыв союзников
    const SKILL_TYPE_HEAL = 'heal';               // Лечение
    const SKILL_TYPE_TELEPORT = 'teleport';       // Телепортация
    const SKILL_TYPE_SHIELD = 'shield';           // Защитный щит

    /**
     * Условия активации скиллов
     */
    const TRIGGER_ON_ATTACK = 'on_attack';        // При атаке
    const TRIGGER_ON_DAMAGED = 'on_damaged';      // При получении урона
    const TRIGGER_LOW_HP = 'low_hp';              // При низком HP
    const TRIGGER_PLAYER_DETECTED = 'player_detected'; // При обнаружении игрока
    const TRIGGER_MULTIPLE_ENEMIES = 'multiple_enemies'; // При множественных врагах
    const TRIGGER_RANDOM = 'random';              // Случайно
    const TRIGGER_PERIODIC = 'periodic';          // Периодически

    protected MineDetectionService $detectionService;
    protected BattleLogService $battleLogService;

    public function __construct(MineDetectionService $detectionService, BattleLogService $battleLogService)
    {
        $this->detectionService = $detectionService;
        $this->battleLogService = $battleLogService;
    }

    /**
     * Обрабатывает все скиллы моба и определяет, какие должны быть активированы
     */
    public function processMobSkills(Mob $mob, ?User $target = null, array $context = []): array
    {
        $activatedSkills = [];

        Log::info('MobSkillFramework: Начинаем обработку скиллов моба', [
            'mob_id' => $mob->id,
            'mob_name' => $mob->name,
            'target_id' => $target?->id,
            'target_name' => $target?->name,
            'context' => $context
        ]);

        // Получаем все скиллы моба с их кулдаунами
        $mobSkills = $mob->skills()->with('skillTemplate')->get();

        Log::info('MobSkillFramework: Найдено скиллов у моба', [
            'mob_id' => $mob->id,
            'skills_count' => $mobSkills->count(),
            'skills' => $mobSkills->map(function ($skill) {
                return [
                    'id' => $skill->id,
                    'template_id' => $skill->skill_template_id,
                    'template_name' => $skill->skillTemplate->name ?? 'Unknown'
                ];
            })->toArray()
        ]);

        if ($mobSkills->isEmpty()) {
            Log::info('MobSkillFramework: У моба нет скиллов', ['mob_id' => $mob->id]);
            return $activatedSkills;
        }

        foreach ($mobSkills as $mobSkill) {
            try {
                Log::info('MobSkillFramework: Проверяем скилл', [
                    'mob_id' => $mob->id,
                    'skill_id' => $mobSkill->id,
                    'template_id' => $mobSkill->skill_template_id,
                    'skill_name' => $mobSkill->skillTemplate->name ?? 'Unknown'
                ]);

                // Проверяем кулдаун
                if (!$this->isSkillAvailable($mobSkill)) {
                    Log::info('MobSkillFramework: Скилл на кулдауне', [
                        'mob_id' => $mob->id,
                        'skill_id' => $mobSkill->id,
                        'skill_name' => $mobSkill->skillTemplate->name ?? 'Unknown'
                    ]);
                    continue;
                }

                // Проверяем условия активации
                if (!$this->checkSkillTrigger($mob, $mobSkill, $target, $context)) {
                    Log::info('MobSkillFramework: Скилл не прошел проверку триггера', [
                        'mob_id' => $mob->id,
                        'skill_id' => $mobSkill->id,
                        'skill_name' => $mobSkill->skillTemplate->name ?? 'Unknown'
                    ]);
                    continue;
                }

                // Проверяем шанс активации
                if (!$this->rollSkillChance($mobSkill)) {
                    Log::info('MobSkillFramework: Скилл не прошел проверку шанса', [
                        'mob_id' => $mob->id,
                        'skill_id' => $mobSkill->id,
                        'skill_name' => $mobSkill->skillTemplate->name ?? 'Unknown'
                    ]);
                    continue;
                }

                Log::info('MobSkillFramework: Активируем скилл', [
                    'mob_id' => $mob->id,
                    'skill_id' => $mobSkill->id,
                    'skill_name' => $mobSkill->skillTemplate->name ?? 'Unknown'
                ]);

                // Активируем скилл
                $skillResult = $this->activateSkill($mob, $mobSkill, $target, $context);

                if ($skillResult['success']) {
                    $activatedSkills[] = $skillResult;

                    // Устанавливаем кулдаун
                    $this->setSkillCooldown($mobSkill);

                    Log::info('Активирован скилл моба', [
                        'mob_id' => $mob->id,
                        'skill_template_id' => $mobSkill->skill_template_id,
                        'skill_name' => $mobSkill->skillTemplate->name ?? 'Unknown',
                        'target_id' => $target?->id,
                        'context' => $context
                    ]);
                } else {
                    Log::info('MobSkillFramework: Скилл не удалось активировать', [
                        'mob_id' => $mob->id,
                        'skill_id' => $mobSkill->id,
                        'skill_name' => $mobSkill->skillTemplate->name ?? 'Unknown',
                        'result' => $skillResult
                    ]);
                }

            } catch (\Exception $e) {
                Log::error('Ошибка обработки скилла моба', [
                    'mob_id' => $mob->id,
                    'skill_id' => $mobSkill->skill_id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        Log::info('MobSkillFramework: Завершена обработка скиллов моба', [
            'mob_id' => $mob->id,
            'activated_skills_count' => count($activatedSkills),
            'activated_skills' => $activatedSkills
        ]);

        return $activatedSkills;
    }

    /**
     * Проверяет, доступен ли скилл (не на кулдауне)
     */
    protected function isSkillAvailable(MobSkill $mobSkill): bool
    {
        if ($mobSkill->cooldown_ends_at && now()->lt($mobSkill->cooldown_ends_at)) {
            return false;
        }

        return true;
    }

    /**
     * Проверяет условия активации скилла
     */
    protected function checkSkillTrigger(Mob $mob, MobSkill $mobSkill, ?User $target, array $context): bool
    {
        $skill = $mobSkill->skill;
        $trigger = $skill->effect_data['trigger'] ?? self::TRIGGER_RANDOM;

        switch ($trigger) {
            case self::TRIGGER_ON_ATTACK:
                return isset($context['event_type']) && $context['event_type'] === 'attack';

            case self::TRIGGER_ON_DAMAGED:
                return isset($context['event_type']) && $context['event_type'] === 'damaged';

            case self::TRIGGER_LOW_HP:
                $hpPercent = $mob->hp / $mob->max_hp;
                $threshold = $skill->effect_data['hp_threshold'] ?? 0.3;
                return $hpPercent <= $threshold;

            case self::TRIGGER_PLAYER_DETECTED:
                if (!$target || !isset($context['mine_location_id'])) {
                    return false;
                }
                return $this->detectionService->isPlayerDetected(
                    $target->id,
                    $mob->location_id,
                    $context['mine_location_id']
                );

            case self::TRIGGER_MULTIPLE_ENEMIES:
                $enemyCount = $context['enemy_count'] ?? 1;
                $minEnemies = $skill->effect_data['min_enemies'] ?? 2;
                return $enemyCount >= $minEnemies;

            case self::TRIGGER_PERIODIC:
                // Проверяем последнее использование
                $interval = $skill->effect_data['interval'] ?? 30; // секунды
                $lastUsed = $mobSkill->updated_at ?? $mobSkill->created_at;
                return now()->diffInSeconds($lastUsed) >= $interval;

            case self::TRIGGER_RANDOM:
            default:
                return true; // Всегда доступен для случайной активации
        }
    }

    /**
     * Проверяет шанс активации скилла
     */
    protected function rollSkillChance(MobSkill $mobSkill): bool
    {
        $template = $mobSkill->skillTemplate;
        if (!$template) {
            return false;
        }

        // Сначала пробуем получить шанс из effect_data, затем из trigger_chance
        $chance = $template->effect_data['trigger_chance'] ?? $template->trigger_chance ?? 0;

        Log::info('MobSkillFramework: Проверка шанса скилла', [
            'skill_id' => $mobSkill->id,
            'template_id' => $template->id,
            'skill_name' => $template->name,
            'chance' => $chance,
            'roll_needed' => $chance > 0
        ]);

        if ($chance <= 0) {
            return false;
        }

        $roll = rand(1, 100);
        $success = $roll <= $chance;

        Log::info('MobSkillFramework: Результат броска шанса', [
            'skill_name' => $template->name,
            'chance' => $chance,
            'roll' => $roll,
            'success' => $success
        ]);

        return $success;
    }

    /**
     * Активирует скилл моба
     */
    protected function activateSkill(Mob $mob, MobSkill $mobSkill, ?User $target, array $context): array
    {
        $skillTemplate = $mobSkill->skillTemplate;
        $skillType = $skillTemplate->effect_type ?? self::SKILL_TYPE_DAMAGE;

        $result = [
            'success' => false,
            'skill_id' => $skillTemplate->id,
            'skill_name' => $skillTemplate->name,
            'skill_type' => $skillType,
            'effects' => [],
            'damage' => 0,
            'message' => ''
        ];

        try {
            switch ($skillType) {
                case self::SKILL_TYPE_DAMAGE:
                    $result = $this->activateDamageSkill($mob, $skillTemplate, $target, $context);
                    break;

                case self::SKILL_TYPE_DEBUFF:
                    $result = $this->activateDebuffSkill($mob, $skillTemplate, $target, $context);
                    break;

                case self::SKILL_TYPE_STUN:
                    $result = $this->activateStunSkill($mob, $skillTemplate, $target, $context);
                    break;

                case self::SKILL_TYPE_AOE:
                    $result = $this->activateAoeSkill($mob, $skillTemplate, $target, $context);
                    break;

                case self::SKILL_TYPE_SELF_BUFF:
                    $result = $this->activateSelfBuffSkill($mob, $skillTemplate, $context);
                    break;

                case self::SKILL_TYPE_DETECTION:
                    $result = $this->activateDetectionSkill($mob, $skillTemplate, $context);
                    break;

                case self::SKILL_TYPE_HEAL:
                    $result = $this->activateHealSkill($mob, $skillTemplate, $context);
                    break;

                case self::SKILL_TYPE_SHIELD:
                    $result = $this->activateShieldSkill($mob, $skillTemplate, $context);
                    break;

                default:
                    Log::warning('Неизвестный тип скилла моба', [
                        'skill_type' => $skillType,
                        'skill_id' => $skillTemplate->id
                    ]);
            }

        } catch (\Exception $e) {
            Log::error('Ошибка активации скилла моба', [
                'mob_id' => $mob->id,
                'skill_id' => $skillTemplate->id,
                'skill_type' => $skillType,
                'error' => $e->getMessage()
            ]);
        }

        return $result;
    }

    /**
     * Активирует скилл урона
     */
    protected function activateDamageSkill(Mob $mob, MobSkillTemplate $skillTemplate, ?User $target, array $context): array
    {
        if (!$target) {
            return ['success' => false, 'message' => 'Нет цели для атаки'];
        }

        $baseDamage = $skillTemplate->effect_data['damage'] ?? $mob->strength;
        $multiplier = $skillTemplate->effect_data['damage_multiplier'] ?? 1.5;
        $damage = round($baseDamage * $multiplier);

        // Применяем урон через существующую систему
        app(PlayerHealthService::class)->applyDamage($target, $damage, "mob_skill:{$mob->id}");

        return [
            'success' => true,
            'skill_type' => self::SKILL_TYPE_DAMAGE,
            'damage' => $damage,
            'target_id' => $target->id,
            'message' => "{$mob->name} использует {$skillTemplate->name} и наносит {$damage} урона!"
        ];
    }

    /**
     * Активирует дебаф скилл
     */
    protected function activateDebuffSkill(Mob $mob, MobSkillTemplate $skillTemplate, ?User $target, array $context): array
    {
        if (!$target) {
            return ['success' => false, 'message' => 'Нет цели для дебафа'];
        }

        $duration = $skillTemplate->effect_data['duration'] ?? $skillTemplate->duration ?? 30;
        $debuffType = $skillTemplate->effect_data['debuff_type'] ?? 'weakness';

        $effect = ActiveEffect::create([
            'effect_type' => $debuffType,
            'target_type' => 'App\\Models\\User',
            'target_id' => $target->id,
            'source_type' => 'App\\Models\\Mob',
            'source_id' => $mob->id,
            'skill_id' => null, // Для мобов используем template_id в effect_data
            'duration' => $duration,
            'ends_at' => now()->addSeconds($duration),
            'power' => $skillTemplate->effect_data['power'] ?? 1,
            'effect_data' => array_merge($skillTemplate->effect_data, ['template_id' => $skillTemplate->id])
        ]);

        return [
            'success' => true,
            'skill_type' => self::SKILL_TYPE_DEBUFF,
            'effect_id' => $effect->id,
            'target_id' => $target->id,
            'debuff_type' => $debuffType,
            'duration' => $duration,
            'message' => "{$mob->name} накладывает {$skillTemplate->name} на {$target->name}!"
        ];
    }

    /**
     * Активирует скилл оглушения
     */
    protected function activateStunSkill(Mob $mob, MobSkillTemplate $skillTemplate, ?User $target, array $context): array
    {
        if (!$target) {
            return ['success' => false, 'message' => 'Нет цели для оглушения'];
        }

        // Получаем параметры оглушения из шаблона
        $duration = $skillTemplate->effect_data['duration'] ?? $skillTemplate->duration ?? 5;
        $disableSkills = $skillTemplate->effect_data['disable_skills'] ?? true;
        $disableMovement = $skillTemplate->effect_data['disable_movement'] ?? true;
        $disableActions = $skillTemplate->effect_data['disable_actions'] ?? true;
        $message = $skillTemplate->effect_data['message'] ?? '⚡ Вы оглушены и не можете действовать!';

        // Создаем эффект оглушения
        $effect = ActiveEffect::create([
            'effect_type' => 'stun',
            'target_type' => 'App\\Models\\User',
            'target_id' => $target->id,
            'source_type' => 'App\\Models\\Mob',
            'source_id' => $mob->id,
            'skill_id' => null, // Для мобов используем template_id в effect_data
            'duration' => $duration,
            'ends_at' => now()->addSeconds($duration),
            'power' => 1,
            'effect_data' => [
                'type' => 'stun',
                'template_id' => $skillTemplate->id,
                'disable_skills' => $disableSkills,
                'disable_movement' => $disableMovement,
                'disable_actions' => $disableActions,
                'message' => $message
            ]
        ]);

        Log::info('Применен эффект оглушения от моба', [
            'mob_id' => $mob->id,
            'mob_name' => $mob->name,
            'target_id' => $target->id,
            'target_name' => $target->name,
            'skill_template_id' => $skillTemplate->id,
            'skill_name' => $skillTemplate->name,
            'duration' => $duration,
            'effect_id' => $effect->id
        ]);

        // Добавляем запись в боевой журнал
        $this->battleLogService->logMobSkillUsage(
            $mob->id,
            $mob->name,
            $target->id,
            $target->name,
            $skillTemplate->name,
            'stun',
            ['duration' => $duration],
            $context['location_key'] ?? 'unknown'
        );

        return [
            'success' => true,
            'skill_type' => self::SKILL_TYPE_STUN,
            'effect_id' => $effect->id,
            'target_id' => $target->id,
            'duration' => $duration,
            'message' => "{$mob->name} использует {$skillTemplate->name} и оглушает {$target->name} на {$duration} секунд!"
        ];
    }

    /**
     * Активирует самобаф
     */
    protected function activateSelfBuffSkill(Mob $mob, MobSkillTemplate $skillTemplate, array $context): array
    {
        $duration = $skillTemplate->effect_data['duration'] ?? $skillTemplate->duration ?? 60;
        $buffType = $skillTemplate->effect_data['buff_type'] ?? 'strength';

        $effect = ActiveEffect::create([
            'effect_type' => $buffType,
            'target_type' => 'App\\Models\\Mob',
            'target_id' => $mob->id,
            'source_type' => 'App\\Models\\Mob',
            'source_id' => $mob->id,
            'skill_id' => null, // Для мобов используем template_id в effect_data
            'duration' => $duration,
            'ends_at' => now()->addSeconds($duration),
            'power' => $skillTemplate->effect_data['power'] ?? 1,
            'effect_data' => array_merge($skillTemplate->effect_data, ['template_id' => $skillTemplate->id])
        ]);

        return [
            'success' => true,
            'skill_type' => self::SKILL_TYPE_SELF_BUFF,
            'effect_id' => $effect->id,
            'buff_type' => $buffType,
            'duration' => $duration,
            'message' => "{$mob->name} использует {$skillTemplate->name} и усиливается!"
        ];
    }

    /**
     * Активирует скилл обнаружения (расширяет область действия дебафа)
     */
    protected function activateDetectionSkill(Mob $mob, MobSkillTemplate $skillTemplate, array $context): array
    {
        // Этот скилл может расширять действие системы обнаружения
        // Например, увеличивать время действия дебафов или их эффективность

        $duration = $skillTemplate->effect_data['detection_duration'] ?? $skillTemplate->duration ?? 120;
        $radius = $skillTemplate->effect_data['detection_radius'] ?? 1;

        // Создаем эффект "Усиленное обнаружение" для моба
        $effect = ActiveEffect::create([
            'effect_type' => 'enhanced_detection',
            'target_type' => 'App\\Models\\Mob',
            'target_id' => $mob->id,
            'source_type' => 'App\\Models\\Mob',
            'source_id' => $mob->id,
            'skill_id' => null, // Для мобов используем template_id в effect_data
            'duration' => $duration,
            'ends_at' => now()->addSeconds($duration),
            'effect_data' => [
                'template_id' => $skillTemplate->id,
                'detection_radius' => $radius,
                'detection_bonus' => $skillTemplate->effect_data['detection_bonus'] ?? 1.5
            ]
        ]);

        return [
            'success' => true,
            'skill_type' => self::SKILL_TYPE_DETECTION,
            'effect_id' => $effect->id,
            'duration' => $duration,
            'radius' => $radius,
            'message' => "{$mob->name} обостряет чувства и начинает лучше обнаруживать врагов!"
        ];
    }

    /**
     * Активирует скилл лечения
     */
    protected function activateHealSkill(Mob $mob, MobSkillTemplate $skillTemplate, array $context): array
    {
        $healAmount = $skillTemplate->effect_data['heal_amount'] ?? round($mob->max_hp * 0.2);
        $newHp = min($mob->max_hp, $mob->hp + $healAmount);
        $actualHeal = $newHp - $mob->hp;

        $mob->hp = $newHp;
        $mob->save();

        return [
            'success' => true,
            'skill_type' => self::SKILL_TYPE_HEAL,
            'heal_amount' => $actualHeal,
            'new_hp' => $newHp,
            'message' => "{$mob->name} восстанавливает {$actualHeal} здоровья!"
        ];
    }

    /**
     * Активирует защитный щит
     */
    protected function activateShieldSkill(Mob $mob, MobSkillTemplate $skillTemplate, array $context): array
    {
        $duration = $skillTemplate->effect_data['duration'] ?? $skillTemplate->duration ?? 30;
        $shieldStrength = $skillTemplate->effect_data['shield_strength'] ?? round($mob->max_hp * 0.3);

        $effect = ActiveEffect::create([
            'effect_type' => 'shield',
            'target_type' => 'App\\Models\\Mob',
            'target_id' => $mob->id,
            'source_type' => 'App\\Models\\Mob',
            'source_id' => $mob->id,
            'skill_id' => null, // Для мобов используем template_id в effect_data
            'duration' => $duration,
            'ends_at' => now()->addSeconds($duration),
            'power' => $shieldStrength,
            'effect_data' => [
                'template_id' => $skillTemplate->id,
                'shield_strength' => $shieldStrength
            ]
        ]);

        return [
            'success' => true,
            'skill_type' => self::SKILL_TYPE_SHIELD,
            'effect_id' => $effect->id,
            'shield_strength' => $shieldStrength,
            'duration' => $duration,
            'message' => "{$mob->name} создает защитный щит силой {$shieldStrength}!"
        ];
    }

    /**
     * Активирует AoE скилл
     */
    protected function activateAoeSkill(Mob $mob, MobSkillTemplate $skillTemplate, ?User $target, array $context): array
    {
        // AoE скиллы требуют более сложной логики поиска целей
        // Пока что заглушка для будущей реализации

        return [
            'success' => true,
            'skill_type' => self::SKILL_TYPE_AOE,
            'message' => "{$mob->name} готовится к мощной атаке по площади!"
        ];
    }

    /**
     * Устанавливает кулдаун для скилла
     */
    protected function setSkillCooldown(MobSkill $mobSkill): void
    {
        $cooldownSeconds = $mobSkill->cooldown ?? 30;
        $mobSkill->cooldown_ends_at = now()->addSeconds($cooldownSeconds);
        $mobSkill->save();
    }

    /**
     * Получает все активные эффекты моба
     */
    public function getMobActiveEffects(Mob $mob): array
    {
        return ActiveEffect::where('target_type', 'App\\Models\\Mob')
            ->where('target_id', $mob->id)
            ->where('ends_at', '>', now())
            ->with('skill')
            ->get()
            ->toArray();
    }

    /**
     * Проверяет, имеет ли моб определенный эффект
     */
    public function mobHasEffect(Mob $mob, string $effectType): bool
    {
        return ActiveEffect::where('target_type', 'App\\Models\\Mob')
            ->where('target_id', $mob->id)
            ->where('effect_type', $effectType)
            ->where('ends_at', '>', now())
            ->exists();
    }
}